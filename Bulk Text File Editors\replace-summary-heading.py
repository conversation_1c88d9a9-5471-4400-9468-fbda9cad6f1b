#!/usr/bin/env python3
"""
Bulk Text File Editor for Markdown Files - Replace Summary Heading

This script allows the user to browse to a folder and bulk edits all .md files found in only the top level of the folder.
It replaces all occurrences of "# Summary" with "# Knowledge Graph" in each markdown file.

Usage:
    python replace-summary-heading.py

The script will open a folder selection dialog for the user to choose the target folder.
"""

import os
import re
import tkinter as tk
from tkinter import filedialog, messagebox


def process_file(file_path):
    """
    Process a markdown file to replace "# Summary" with "# Knowledge Graph".

    Args:
        file_path (str): Path to the markdown file

    Returns:
        tuple: (bool, int) - Success status and count of replacements made
    """
    try:
        # Read the file content
        with open(file_path, 'r', encoding='utf-8') as file:
            content = file.read()

        # Replace "# Summary" with "# Knowledge Graph"
        pattern = r'^# Summary$'
        modified_content, replacements = re.subn(pattern, '# Knowledge Graph', content, flags=re.MULTILINE)

        # If changes were made, write back to the file
        if replacements > 0:
            with open(file_path, 'w', encoding='utf-8') as file:
                file.write(modified_content)
            return True, replacements

        return False, 0

    except Exception as e:
        print(f"Error processing {file_path}: {str(e)}")
        return False, 0


def process_folder(folder_path):
    """
    Process all markdown files in the specified folder.

    Args:
        folder_path (str): Path to the folder containing markdown files

    Returns:
        tuple: (total_files, modified_files, total_replacements)
    """
    # Check if the folder exists
    if not os.path.isdir(folder_path):
        messagebox.showerror("Error", f"The folder '{folder_path}' does not exist.")
        return 0, 0, 0

    print(f"Processing markdown files in: {folder_path}")

    # Get all markdown files in the top level of the folder
    md_files = [f for f in os.listdir(folder_path) if f.endswith('.md') and os.path.isfile(os.path.join(folder_path, f))]

    if not md_files:
        print("No markdown files found in the specified folder.")
        return 0, 0, 0

    print(f"Found {len(md_files)} markdown files.")

    # Process each file
    total_files_modified = 0
    total_replacements = 0

    for file_name in md_files:
        file_path = os.path.join(folder_path, file_name)
        modified, replacements = process_file(file_path)

        if modified:
            total_files_modified += 1
            total_replacements += replacements
            print(f"Modified: {file_name} - Replaced {replacements} occurrences of '# Summary' with '# Knowledge Graph'")

    return len(md_files), total_files_modified, total_replacements


def main():
    """Main function to browse for a folder and process markdown files."""
    # Create a root window but hide it
    root = tk.Tk()
    root.withdraw()

    # Show folder selection dialog
    folder_path = filedialog.askdirectory(
        title="Select Folder with Markdown Files",
        mustexist=True
    )

    # If user cancels the dialog
    if not folder_path:
        print("Folder selection cancelled.")
        return

    # Process the selected folder
    total_files, modified_files, total_replacements = process_folder(folder_path)

    # Print summary
    if total_files > 0:
        print("\nSummary:")
        print(f"Total files processed: {total_files}")
        print(f"Files modified: {modified_files}")
        print(f"Total replacements made: {total_replacements}")

        # Show a message box with the summary
        messagebox.showinfo(
            "Processing Complete",
            f"Total files processed: {total_files}\n"
            f"Files modified: {modified_files}\n"
            f"Total replacements made: {total_replacements}"
        )


if __name__ == "__main__":
    main()
