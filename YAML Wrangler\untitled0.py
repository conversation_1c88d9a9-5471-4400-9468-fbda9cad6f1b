import os
import yaml

def extract_yaml_keys(file_path):
    try:
        with open(file_path, 'r', encoding='utf-8') as file:
            content = file.read()
            
            # Check if file has YAML front matter
            if content.startswith('---'):
                front_matter_end = content.find('---', 3)
                front_matter = content[3:front_matter_end]
                front_matter_dict = yaml.safe_load(front_matter)
                return set(front_matter_dict.keys())
    except yaml.YAMLError as e:
        print(f"Error parsing YAML in file {file_path}: {e}")
    
    return set()

def scan_markdown_files(directory):
    scanned_files = 0
    all_keys = set()

    for filename in os.listdir(directory):
        if filename.endswith('.md'):
            file_path = os.path.join(directory, filename)
            scanned_files += 1
            keys = extract_yaml_keys(file_path)
            all_keys.update(keys)

    print(f"Scanned {scanned_files} files.")
    print(f"Found YAML keys: {all_keys}")

if __name__ == "__main__":
    directory = "Test"
    scan_markdown_files(directory)