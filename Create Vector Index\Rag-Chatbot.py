import tkinter as tk
from tkinter import filedialog, scrolledtext, messagebox
import json
import requests
import numpy as np # For cosine similarity
import threading
import os # Moved import os to the top

# --- Configuration ---
# LM Studio Endpoints
EMBEDDING_API_URL = "http://127.0.0.1:7856/v1/embeddings"
CHAT_API_URL = "http://127.0.0.1:7856/v1/chat/completions" # Standard for LM Studio

# Model Names (ensure these match what's loaded in LM Studio)
EMBEDDING_MODEL_NAME = "nomic-ai/nomic-embed-text-v1.5"
LLM_MODEL_NAME = "qwen3-32b" # As specified by user

# --- Global Variables ---
vector_database = []
db_filepath = ""

# --- Helper Functions ---

def get_embedding(text_content):
    """Computes embedding vector for the given text using LM Studio API."""
    headers = {"Content-Type": "application/json"}
    data = {"model": EMBEDDING_MODEL_NAME, "input": text_content}
    try:
        response = requests.post(EMBEDDING_API_URL, headers=headers, json=data, timeout=60)
        response.raise_for_status()
        result = response.json()
        if "data" in result and isinstance(result["data"], list) and len(result["data"]) > 0:
            if "embedding" in result["data"][0]:
                return result["data"][0]["embedding"]
        print(f"Warning: Could not extract embedding from API response: {result}")
        return None
    except requests.exceptions.RequestException as e:
        print(f"Error calling embedding API: {e}")
        messagebox.showerror("API Error", f"Error calling embedding API: {e}")
        return None
    except json.JSONDecodeError:
        print(f"Error decoding JSON response from embedding API.")
        messagebox.showerror("API Error", "Error decoding JSON response from embedding API.")
        return None

def cosine_similarity(vec1, vec2):
    """Computes cosine similarity between two vectors."""
    if vec1 is None or vec2 is None:
        return 0.0
    vec1 = np.array(vec1)
    vec2 = np.array(vec2)
    dot_product = np.dot(vec1, vec2)
    norm_vec1 = np.linalg.norm(vec1)
    norm_vec2 = np.linalg.norm(vec2)
    if norm_vec1 == 0 or norm_vec2 == 0:
        return 0.0
    return dot_product / (norm_vec1 * norm_vec2)

def find_top_n_similar(query_embedding, db, n=3):
    """Finds top N similar entries from the database."""
    if not query_embedding or not db:
        return []
    
    similarities = []
    for entry in db:
        entry_vec = entry.get("embedding", {}).get(EMBEDDING_MODEL_NAME, {}).get("vec")
        if entry_vec:
            sim = cosine_similarity(query_embedding, entry_vec)
            similarities.append((sim, entry))
            
    similarities.sort(key=lambda x: x[0], reverse=True)
    return [entry for sim, entry in similarities[:n]]

def query_llm(prompt_text):
    """Queries the local LLM with the given prompt."""
    headers = {"Content-Type": "application/json"}
    payload = {
        "model": LLM_MODEL_NAME,
        "messages": [
            {"role": "system", "content": "You are a helpful assistant. Use the provided context to answer the user's question."},
            {"role": "user", "content": prompt_text}
        ],
        "temperature": 0.65, # Adjust as needed
        "max_tokens": 30000   # Adjust as needed
    }
    try:
        response = requests.post(CHAT_API_URL, headers=headers, json=payload, timeout=180) # Longer timeout for LLM
        response.raise_for_status()
        result = response.json()
        if "choices" in result and len(result["choices"]) > 0:
            return result["choices"][0]["message"]["content"].strip()
        else:
            print(f"LLM API did not return expected choices: {result}")
            return "Error: LLM did not return a valid response."
    except requests.exceptions.RequestException as e:
        print(f"Error calling LLM API: {e}")
        messagebox.showerror("LLM API Error", f"Error calling LLM API: {e}")
        return f"Error: Could not connect to LLM - {e}"
    except json.JSONDecodeError:
        print(f"Error decoding JSON response from LLM API.")
        messagebox.showerror("LLM API Error", "Error decoding JSON response from LLM API.")
        return "Error: Could not decode LLM response."

# --- UI Functions ---

def load_vector_db():
    global vector_database, db_filepath
    filepath = filedialog.askopenfilename(
        title="Select Vector Database JSON File",
        filetypes=(("JSON files", "*.json"), ("All files", "*.*"))
    )
    if filepath:
        try:
            with open(filepath, 'r', encoding='utf-8') as f:
                vector_database = json.load(f)
            db_filepath = filepath
            db_path_entry.configure(state='normal')
            db_path_entry.delete(0, tk.END)
            db_path_entry.insert(0, filepath)
            db_path_entry.configure(state='readonly')
            chat_display.configure(state='normal')
            chat_display.insert(tk.END, f"System: Vector database '{os.path.basename(filepath)}' loaded. {len(vector_database)} entries.\n")
            chat_display.configure(state='disabled')
            messagebox.showinfo("Success", f"Database loaded with {len(vector_database)} entries.")
        except Exception as e:
            messagebox.showerror("Error Loading DB", f"Failed to load database: {e}")
            vector_database = []
            db_filepath = ""

def handle_send_question():
    user_question = question_entry.get().strip()
    if not user_question:
        messagebox.showwarning("Input Error", "Please enter a question.")
        return
    if not vector_database:
        messagebox.showwarning("DB Error", "Please load a vector database first.")
        return

    chat_display.configure(state='normal')
    chat_display.insert(tk.END, f"You: {user_question}\n")
    chat_display.see(tk.END)
    chat_display.configure(state='disabled')
    question_entry.delete(0, tk.END)
    
    # Disable send button during processing
    send_button.config(state=tk.DISABLED)
    loading_label.config(text="Processing...")

    # Run RAG in a separate thread to keep UI responsive
    thread = threading.Thread(target=process_question_thread, args=(user_question,))
    thread.start()

def process_question_thread(user_question):
    try:
        # 1. Get embedding for the user question
        query_embedding = get_embedding(user_question)
        if not query_embedding:
            update_chat_display("System: Could not get embedding for your question.\n")
            return

        # 2. Find relevant context from DB
        relevant_docs = find_top_n_similar(query_embedding, vector_database, n=30)
        
        context_str = ""
        if relevant_docs:
            context_str = "\n\nContext from knowledge base:\n"
            for i, doc in enumerate(relevant_docs):
                context_str += f"{i+1}. From file '{os.path.basename(doc.get('filepath', 'N/A'))}', line {doc.get('line_number', 'N/A')}:\n\"{doc.get('text', '')}\"\n\n"
        else:
            context_str = "\n\nNo specific context found in the knowledge base for your question."

        # 3. Formulate prompt and query LLM
        # prompt = f"User question: \"{user_question}\"\n\n{context_str}\n\nBased on the context (if provided and relevant), answer the user's question. If the context isn't relevant or doesn't help, answer based on your general knowledge."
        prompt = (
            f"You are an AI assistant. Based on the following context (if provided and relevant), "
            f"answer the user's question. If the context isn't relevant or doesn't help, "
            f"try to answer based on your general knowledge but indicate if you are doing so.\n\n"
            f"Context:\n{context_str if relevant_docs else 'No specific context provided from knowledge base.'}\n\n"
            f"User's Question: {user_question}"
        )

        llm_response = query_llm(prompt)
        update_chat_display(f"AI: {llm_response}\n")

    except Exception as e:
        print(f"Error in processing thread: {e}")
        update_chat_display(f"System: An error occurred: {e}\n")
    finally:
        # Re-enable send button and clear loading label on the main thread
        app.after(0, reset_ui_after_processing)

def update_chat_display(message):
    def _update():
        chat_display.configure(state='normal')
        chat_display.insert(tk.END, message)
        chat_display.see(tk.END)
        chat_display.configure(state='disabled')
    app.after(0, _update) # Schedule UI update on the main thread

def reset_ui_after_processing():
    send_button.config(state=tk.NORMAL)
    loading_label.config(text="")


# --- Main Application Setup ---
app = tk.Tk()
app.title("RAG Chatbot")
app.geometry("700x600")

# Frame for DB Path
db_frame = tk.Frame(app, pady=5)
db_frame.pack(fill=tk.X)

tk.Label(db_frame, text="Vector DB Path:").pack(side=tk.LEFT, padx=5)
db_path_entry = tk.Entry(db_frame, width=60, state='readonly')
db_path_entry.pack(side=tk.LEFT, expand=True, fill=tk.X, padx=5)
browse_button = tk.Button(db_frame, text="Browse", command=load_vector_db)
browse_button.pack(side=tk.LEFT, padx=5)

# Chat Display Area
chat_display = scrolledtext.ScrolledText(app, wrap=tk.WORD, state='disabled', height=20)
chat_display.pack(padx=10, pady=5, expand=True, fill=tk.BOTH)

# Frame for Question Input
input_frame = tk.Frame(app, pady=5)
input_frame.pack(fill=tk.X)

tk.Label(input_frame, text="Your Question:").pack(side=tk.LEFT, padx=5)
question_entry = tk.Entry(input_frame, width=60)
question_entry.pack(side=tk.LEFT, expand=True, fill=tk.X, padx=5)
question_entry.bind("<Return>", lambda event: handle_send_question()) # Bind Enter key

send_button = tk.Button(input_frame, text="Send", command=handle_send_question)
send_button.pack(side=tk.LEFT, padx=5)

# Loading Label
loading_label = tk.Label(app, text="", fg="blue")
loading_label.pack(pady=2)

if __name__ == "__main__":
    app.mainloop()
