import os
import yaml
import subprocess
import platform

def extract_yaml_keys(file_path):
    try:
        with open(file_path, 'r', encoding='utf-8') as file:
            content = file.read()
            
            # Check if file has YAML front matter
            if content.startswith('---'):
                front_matter_end = content.find('---', 3)
                front_matter = content[3:front_matter_end]
                front_matter_dict = yaml.safe_load(front_matter)
                return front_matter_dict.keys()
    except yaml.YAMLError as e:
        print(f"Error parsing YAML in file {file_path}: {e}")
    
    return set()

def find_files_with_key(directory, search_key):
    files_with_key = []

    for filename in os.listdir(directory):
        if filename.endswith('.md'):
            file_path = os.path.join(directory, filename)
            keys = extract_yaml_keys(file_path)
            if search_key in keys:
                files_with_key.append(file_path)

    return files_with_key

def open_files_in_editor(files):
    if platform.system() == 'Windows':
        # Use the default text editor on Windows
        for file in files:
            os.startfile(file)
    elif platform.system() == 'Darwin':
        # Use the default text editor on macOS
        subprocess.call(['open'] + files)
    else:
        # Use the default text editor on Linux
        subprocess.call(['xdg-open'] + files)

if __name__ == "__main__":
    directory = "Test"
    search_key = "Goal"  # Replace with the key you're looking for

    files_to_open = find_files_with_key(directory, search_key)
    num_files = len(files_to_open)
    print(f"Found {num_files} files with the key '{search_key}'.")

    if num_files > 0:
        proceed = input(f"Do you want to open all {num_files} files? (yes/no): ").strip().lower()
        if proceed == 'yes':
            open_files_in_editor(files_to_open)
        else:
            print("Aborted opening files.")