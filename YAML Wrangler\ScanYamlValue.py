import os
import yaml

def extract_values_for_key(file_path, search_key):
    try:
        with open(file_path, 'r', encoding='utf-8') as file:
            content = file.read()
            
            # Check if file has YAML front matter
            if content.startswith('---'):
                front_matter_end = content.find('---', 3)
                if front_matter_end == -1:
                    return None
                front_matter = content[3:front_matter_end]
                front_matter_dict = yaml.safe_load(front_matter)
                
                if search_key in front_matter_dict:
                    return front_matter_dict[search_key]
    except yaml.YAMLError as e:
        print(f"Error parsing YAML in file {file_path}: {e}")
    return None

def scan_files_for_key_values(directory, search_key):
    scanned_files = 0
    key_values = set()

    for filename in os.listdir(directory):
        if filename.endswith('.md'):
            file_path = os.path.join(directory, filename)
            scanned_files += 1
            value = extract_values_for_key(file_path, search_key)
            if value is not None:
                key_values.add(value)

    print(f"Scanned {scanned_files} files.")
    print(f"Found {len(key_values)} unique values for the key '{search_key}':")
    for value in key_values:
        print(value)

if __name__ == "__main__":
    directory = "Test"  # Replace with your directory if different
    search_key = input("Enter the key to find values for: ").strip()
    
    scan_files_for_key_values(directory, search_key)
