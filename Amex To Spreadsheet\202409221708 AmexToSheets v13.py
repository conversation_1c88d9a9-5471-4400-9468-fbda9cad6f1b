import re
from datetime import datetime
from tkinter import Tk
from tkinter.filedialog import askopen<PERSON>lename, asksave<PERSON><PERSON>lena<PERSON>

def reformat_transaction(input_text):
    transactions = []

    # Split the input text into lines
    lines = input_text.strip().split('\n')

    # Skip the header line
    lines = lines[1:]

    for line in lines:
        # Split each line by commas, handling potential embedded commas in the description
        parts = [p.strip() for p in re.split(r',(?=(?:[^"]*"[^"]*")*[^"]*$)', line)]

        date_str = parts[0]
        description_str = parts[1]
        amount_str = parts[4]

        # Reformat date
        date_obj = datetime.strptime(date_str, "%m/%d/%Y")
        formatted_month = date_obj.strftime("%b").upper()
        formatted_day = str(date_obj.day)

        # Clean up description and amount
        description_str = ' '.join(description_str.strip().split())
        amount_str = amount_str.replace('(', '-').replace(')', '').replace('$', '').replace(',', '').strip()
        amount_str = reverse_sign(amount_str)  # Reverse the sign of the amount

        # Store transaction data as a tuple
        transactions.append((date_obj, formatted_month, formatted_day, description_str, amount_str))

    # Sort transactions by date in ascending order
    transactions.sort(key=lambda x: x[0])

    # Format the sorted transactions
    formatted_transactions = [
        f"{formatted_month}\t{formatted_day}\tST\t{description_str}\t\t{amount_str}"
        for _, formatted_month, formatted_day, description_str, amount_str in transactions
    ]

    # Join all formatted transactions into a single output text with line breaks
    return '\n'.join(formatted_transactions)

def reverse_sign(amount_str):
    # Reverse the sign of the amount
    if amount_str.startswith('-'):
        return amount_str[1:]
    else:
        return '-' + amount_str

def process_transactions():
    try:
        # Use tkinter to ask the user for the input file path
        Tk().withdraw()  # Hide the root window
        input_file_path = askopenfilename(title="Select the input text file", filetypes=[("Text files", "*.txt")])
        
        if not input_file_path:
            print("No file selected. Exiting.")
            return

        with open(input_file_path, 'r') as file:
            input_text = file.read()
        
        output_text = reformat_transaction(input_text)

        # Use tkinter to ask the user for the output file path
        output_file_path = asksaveasfilename(title="Save output as", defaultextension=".txt", filetypes=[("Text files", "*.txt")])

        if not output_file_path:
            print("No output file selected. Exiting.")
            return

        with open(output_file_path, 'w') as file:
            file.write(output_text + '\n')

        print(f"Output written successfully to {output_file_path}")

    except Exception as e:
        print(f"An error occurred: {e}")

# Run the process_transactions function
process_transactions()