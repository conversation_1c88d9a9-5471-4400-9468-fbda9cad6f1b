import os
import tkinter as tk
from tkinter import filedialog
from pathlib import Path

# Try to import PyYAM<PERSON>
try:
    import yaml
    YAML_AVAILABLE = True
except ImportError:
    YAML_AVAILABLE = False
    print("PyYAML not available. Install with: pip install PyYAML")
    print("Will use basic text parsing instead (limited functionality).")

def browse_for_folder():
    """Show folder selection dialog."""
    root = tk.Tk()
    root.withdraw()
    
    folder_path = filedialog.askdirectory(title="Select folder containing Markdown files to modify")
    root.destroy()
    
    return folder_path

def update_yaml_value_basic(file_path, search_key, find_value, replace_value):
    """
    Basic YAML front matter value replacement without PyYAML (limited functionality).
    Only handles simple key: value pairs.
    """
    try:
        with open(file_path, 'r', encoding='utf-8') as file:
            content = file.read()

        # Check if file has YAML front matter
        if not content.startswith('---'):
            return False, False, "No YAML front matter found"

        # Find the end of front matter
        front_matter_end = content.find('---', 3)
        if front_matter_end == -1:
            return False, False, "Invalid YAML front matter (no closing ---)"

        front_matter_text = content[3:front_matter_end]
        remaining_content = content[front_matter_end:]

        # Simple text-based replacement for key: value pairs
        import re
        pattern = rf'^({re.escape(search_key)}:\s*)({re.escape(find_value)})(\s*)$'

        new_front_matter, count = re.subn(pattern, rf'\1{replace_value}\3', front_matter_text, flags=re.MULTILINE)

        if count > 0:
            new_content = f"---{new_front_matter}{remaining_content}"
            with open(file_path, 'w', encoding='utf-8') as file:
                file.write(new_content)
            return True, True, "Successfully modified (basic mode)"
        else:
            # Check if key exists
            key_pattern = rf'^{re.escape(search_key)}:'
            if re.search(key_pattern, front_matter_text, re.MULTILINE):
                return True, False, f"Key found but value '{find_value}' not matched"
            else:
                return False, False, f"Key '{search_key}' not found"

    except Exception as e:
        return False, False, f"Error processing file: {e}"

def update_yaml_value(file_path, search_key, find_value, replace_value):
    """
    Update YAML front matter value in a markdown file.
    Returns (has_key, was_modified, error_message)
    """
    if not YAML_AVAILABLE:
        return update_yaml_value_basic(file_path, search_key, find_value, replace_value)

    try:
        with open(file_path, 'r', encoding='utf-8') as file:
            content = file.read()

        # Check if file has YAML front matter
        if not content.startswith('---'):
            return False, False, "No YAML front matter found"

        # Find the end of front matter
        front_matter_end = content.find('---', 3)
        if front_matter_end == -1:
            return False, False, "Invalid YAML front matter (no closing ---)"

        # Extract and parse YAML
        front_matter_text = content[3:front_matter_end].strip()
        try:
            front_matter_dict = yaml.safe_load(front_matter_text)
        except yaml.YAMLError as e:
            return False, False, f"YAML parsing error: {e}"
        
        if front_matter_dict is None:
            front_matter_dict = {}
        
        # Check if the key exists
        if search_key not in front_matter_dict:
            return False, False, f"Key '{search_key}' not found"
        
        # Get the current value
        current_value = front_matter_dict[search_key]
        modified = False
        
        # Handle different value types
        if isinstance(current_value, str):
            # Simple string replacement
            if current_value == find_value:
                front_matter_dict[search_key] = replace_value
                modified = True
        elif isinstance(current_value, list):
            # Handle list values - replace matching items
            new_list = []
            for item in current_value:
                if str(item) == find_value:
                    new_list.append(replace_value)
                    modified = True
                else:
                    new_list.append(item)
            if modified:
                front_matter_dict[search_key] = new_list
        else:
            # Handle other types by converting to string for comparison
            if str(current_value) == find_value:
                # Try to maintain the original type if possible
                if isinstance(current_value, (int, float, bool)):
                    try:
                        # Try to convert replace_value to the same type
                        if isinstance(current_value, int):
                            front_matter_dict[search_key] = int(replace_value)
                        elif isinstance(current_value, float):
                            front_matter_dict[search_key] = float(replace_value)
                        elif isinstance(current_value, bool):
                            front_matter_dict[search_key] = replace_value.lower() in ('true', 'yes', '1')
                        modified = True
                    except ValueError:
                        # If conversion fails, use string
                        front_matter_dict[search_key] = replace_value
                        modified = True
                else:
                    front_matter_dict[search_key] = replace_value
                    modified = True
        
        # If modified, write back to file
        if modified:
            # Generate new YAML front matter
            new_front_matter = yaml.dump(front_matter_dict, default_flow_style=False, allow_unicode=True)
            
            # Reconstruct the file content
            remaining_content = content[front_matter_end + 3:]
            new_content = f"---\n{new_front_matter}---{remaining_content}"
            
            # Write back to file
            with open(file_path, 'w', encoding='utf-8') as file:
                file.write(new_content)
            
            return True, True, "Successfully modified"
        else:
            return True, False, f"Key found but value '{find_value}' not matched"
    
    except Exception as e:
        return False, False, f"Error processing file: {e}"

def bulk_replace_yaml_values(folder_path, search_key, find_value, replace_value):
    """Process all markdown files in the folder and replace YAML values."""
    folder = Path(folder_path)
    
    # Find all markdown files in the top level only
    md_files = [f for f in folder.iterdir() if f.is_file() and f.suffix.lower() == '.md']
    
    if not md_files:
        print("No Markdown files found in the selected folder.")
        return
    
    print(f"Found {len(md_files)} Markdown files to process.")
    print("-" * 60)
    
    processed_count = 0
    modified_count = 0
    error_count = 0
    key_found_count = 0
    
    for md_file in md_files:
        print(f"Processing: {md_file.name}")
        
        has_key, was_modified, message = update_yaml_value(md_file, search_key, find_value, replace_value)
        
        if has_key:
            key_found_count += 1
            if was_modified:
                modified_count += 1
                print(f"  ✓ Modified: {find_value} → {replace_value}")
            else:
                print(f"  - {message}")
        else:
            if "not found" in message.lower():
                print(f"  - Key '{search_key}' not found")
            else:
                print(f"  ✗ Error: {message}")
                error_count += 1
        
        processed_count += 1
    
    print("-" * 60)
    print(f"Summary:")
    print(f"  Files processed: {processed_count}")
    print(f"  Files with key '{search_key}': {key_found_count}")
    print(f"  Files modified: {modified_count}")
    print(f"  Errors: {error_count}")

def main():
    print("Bulk YAML Front Matter Value Replace")
    print("=" * 60)
    print("This script modifies YAML front matter values in Markdown files")
    if YAML_AVAILABLE:
        print("Supports string values, lists, and other data types")
    else:
        print("WARNING: PyYAML not installed - using basic mode")
        print("Only simple key: value pairs supported")
        print("Install PyYAML with: pip install PyYAML")
    print("=" * 60)
    
    # Browse for folder
    folder_path = browse_for_folder()
    
    if not folder_path:
        print("No folder selected. Exiting.")
        return
    
    print(f"\nSelected folder: {folder_path}")
    
    # Get search parameters
    print("\nEnter the YAML key to search through:")
    search_key = input("YAML key: ").strip()
    
    if not search_key:
        print("No key entered. Exiting.")
        return
    
    print(f"\nEnter the value to search for in '{search_key}':")
    find_value = input("Value to find: ").strip()
    
    if not find_value:
        print("No search value entered. Exiting.")
        return
    
    print(f"\nEnter the replacement value:")
    replace_value = input("Replace with: ").strip()
    
    print(f"\nWill replace '{find_value}' with '{replace_value}' in key '{search_key}'")
    
    # Process the folder
    bulk_replace_yaml_values(folder_path, search_key, find_value, replace_value)
    
    print("\nOperation completed!")

if __name__ == "__main__":
    main()
