import os
import tkinter as tk
from tkinter import filedialog

def format_transactions_from_file(input_file_path, output_file_path, blacklist):
    # Read the raw input text from the file
    with open(input_file_path, 'r') as file:
        raw_text = file.read()

    # Split the input into lines and filter out empty lines
    lines = [line.strip() for line in raw_text.strip().split("\n") if line.strip()]

    # Filter out lines containing any blacklist terms
    lines_filtered = []
    for line in lines:
        if not any(blacklist_word in line for blacklist_word in blacklist):
            lines_filtered.append(line)

    # Process the filtered text to format it
    formatted_lines = []
    for i in range(0, len(lines_filtered), 3):
        # In case the structure slightly varies, adjust the slicing accordingly
        date, description, value = lines_filtered[i:i+3]
        month, day = date.split(" ")
        # Handle the conversion of the value, checking for a leading minus sign
        if value.startswith('-'):
            value = value.replace('-', '').replace('$', '')  # Remove minus and dollar sign
        else:
            value = f"-{value.replace('$', '')}"  # Add minus and remove dollar sign
        formatted_line = f"{month.upper()}\t{day}\tAmex\t{description}\t\t\t{value}"
        formatted_lines.append(formatted_line)

    formatted_output = "\n".join(formatted_lines)

    # Write the formatted output to the output file
    with open(output_file_path, 'w') as file:
        file.write(formatted_output)

    print(f"Formatted transactions have been written to {output_file_path}")

def main():
    # Create a graphical window for file browsing
    root = tk.Tk()
    root.withdraw()  # Hide the main window

    # Prompt the user to select an input file
    input_file_path = filedialog.askopenfilename(title="Select the INPUT.txt file")

    if not input_file_path:
        print("No file selected, exiting.")
        return

    # Determine output file path in the same directory as the input file
    directory = os.path.dirname(input_file_path)
    output_file_path = os.path.join(directory, "Output.txt")

    # Define the blacklist
    blacklist = ["Credit", "2X Miles", "Pending", "3X Points", "5X Points"]

    # Call the function with the updated paths and blacklist
    format_transactions_from_file(input_file_path, output_file_path, blacklist)

if __name__ == "__main__":
    main()