import os
import yaml
from datetime import datetime

def get_creation_date(file_path):
    try:
        # For Windows
        if os.name == 'nt':
            return datetime.fromtimestamp(os.path.getctime(file_path)).strftime('%Y%m%d')
        # For Unix-like systems
        else:
            stat = os.stat(file_path)
            return datetime.fromtimestamp(stat.st_birthtime).strftime('%Y%m%d')
    except AttributeError:
        # Fallback to last modified date if creation date is not available
        return datetime.fromtimestamp(os.path.getmtime(file_path)).strftime('%Y%m%d')

def update_yaml_key(file_path, old_key, new_key):
    try:
        with open(file_path, 'r+', encoding='utf-8') as file:
            content = file.read()
            
            # Check if file has YAML front matter
            if content.startswith('---'):
                front_matter_end = content.find('---', 3)
                if front_matter_end == -1:
                    return False
                front_matter = content[3:front_matter_end]
                front_matter_dict = yaml.safe_load(front_matter)
                
                if old_key in front_matter_dict:
                    front_matter_dict[new_key] = front_matter_dict.pop(old_key)
                    
                    new_front_matter = yaml.dump(front_matter_dict, default_flow_style=False)
                    new_front_matter = f"---\n{new_front_matter}---\n"
                    
                    file.seek(0)
                    file.write(new_front_matter + content[front_matter_end+3:])
                    file.truncate()
                    
                    return True
            else:
                # If no YAML front matter, add the default YAML
                creation_date = get_creation_date(file_path)
                front_matter = {
                    'date': creation_date,
                    'category': ''
                }
                yaml_front_matter = yaml.dump(front_matter, default_flow_style=False)
                yaml_front_matter = f"---\n{yaml_front_matter}---\n"
                
                file.seek(0)
                file.write(yaml_front_matter + content)
                return True
    except yaml.YAMLError as e:
        print(f"Error parsing YAML in file {file_path}: {e}")
    return False

def scan_and_update_files(directory, old_key, new_key):
    scanned_files = 0
    updated_files = 0

    for filename in os.listdir(directory):
        if filename.endswith('.md'):
            file_path = os.path.join(directory, filename)
            scanned_files += 1
            if update_yaml_key(file_path, old_key, new_key):
                updated_files += 1

    print(f"Scanned {scanned_files} files.")
    print(f"Updated {updated_files} files.")

if __name__ == "__main__":
    directory = "Test"  # Replace with your directory if different
    old_key = input("Enter the key to find: ").strip()
    new_key = input("Enter the key to replace it with: ").strip()
    
    scan_and_update_files(directory, old_key, new_key)

