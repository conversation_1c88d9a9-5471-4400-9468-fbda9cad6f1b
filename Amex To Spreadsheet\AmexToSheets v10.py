import tkinter as tk
from tkinter import filedialog
import os
import re

def is_date(line):
    """Check if the line contains a date."""
    date_pattern = re.compile(r'\b(Jan|Feb|Mar|Apr|May|Jun|Jul|Aug|Sep|Oct|Nov|Dec)\b')
    return date_pattern.search(line) is not None

def is_value(line):
    """Check if the line contains a monetary value."""
    return '$' in line

def write_date(output_file, line):
    """Write a date line with the format: date<TAB>Amex"""
    output_file.write(f"{line.strip()}\tAmex\n")

def write_description(output_file, line):
    """Write a description line."""
    output_file.write(line)

def write_value(output_file, line):
    """Write a value line with format: <TAB><TAB>value"""
    output_file.write(f"\t\t{line.strip()}\n")

def process_line(output_file, line):
    """Determine the type of line and write to output accordingly."""
    if is_date(line):
        write_date(output_file, line)
    elif is_value(line):
        write_value(output_file, line)
    else:
        write_description(output_file, line)

def process_file(filepath, output_path):
    """Process the input file and output results based on line type."""
    with open(filepath, 'r', encoding='utf-8') as file, open(output_path, 'w', encoding='utf-8') as output_file:
        for line in file:
            process_line(output_file, line)

def select_file_and_process():
    """Open a file dialog to select a text file, process it, and output categorized lines."""
    root = tk.Tk()
    root.withdraw()  # Hide the main window

    file_path = filedialog.askopenfilename(
        title="Select a text file",
        filetypes=(("Text files", "*.txt"), ("All files", "*.*"))
    )

    if file_path:
        output_path = os.path.join(os.path.dirname(file_path), 'Output.txt')
        process_file(file_path, output_path)
        print(f"Processed data has been written to 'Output.txt' in the same directory as the input file.")
    else:
        print("No file was selected.")

if __name__ == "__main__":
    select_file_and_process()