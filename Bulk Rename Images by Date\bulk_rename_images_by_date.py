import os
import tkinter as tk
from tkinter import filedialog, messagebox
from datetime import datetime
from pathlib import Path
import shutil

# Try to import PIL for EXIF data, install if not available
try:
    from PIL import Image
    from PIL.ExifTags import TAGS
    PIL_AVAILABLE = True
except ImportError:
    PIL_AVAILABLE = False
    print("PIL (Pillow) not available. Install with: pip install Pillow")
    print("Will use file system dates only.")

# Common image and video extensions
MEDIA_EXTENSIONS = {
    # Image formats
    '.jpg', '.jpeg', '.png', '.gif', '.bmp', '.tiff', '.tif', '.webp', '.raw', '.cr2', '.nef', '.arw', '.dng',
    # Video formats
    '.mov', '.mp4', '.avi', '.mkv', '.wmv', '.flv', '.m4v', '.3gp', '.webm', '.ogv', '.mts', '.m2ts'
}

def get_file_dates(file_path):
    """Get creation and modification dates from file system."""
    try:
        stat = os.stat(file_path)
        created = datetime.fromtimestamp(stat.st_ctime)
        modified = datetime.fromtimestamp(stat.st_mtime)
        return created, modified
    except Exception as e:
        print(f"Error getting file dates for {file_path}: {e}")
        return None, None

def get_exif_date(file_path):
    """Extract date from EXIF data if available."""
    if not PIL_AVAILABLE:
        return None
    
    try:
        with Image.open(file_path) as image:
            exif_data = image._getexif()
            if exif_data is not None:
                for tag_id, value in exif_data.items():
                    tag = TAGS.get(tag_id, tag_id)
                    if tag in ['DateTime', 'DateTimeOriginal', 'DateTimeDigitized']:
                        try:
                            # EXIF date format: "YYYY:MM:DD HH:MM:SS"
                            return datetime.strptime(value, "%Y:%m:%d %H:%M:%S")
                        except ValueError:
                            continue
    except Exception as e:
        print(f"Error reading EXIF data from {file_path}: {e}")
    
    return None

def get_earliest_date(file_path):
    """Get the earliest date from all available sources."""
    dates = []
    
    # Get EXIF date (usually the most accurate for photos)
    exif_date = get_exif_date(file_path)
    if exif_date:
        dates.append(exif_date)
    
    # Get file system dates
    created, modified = get_file_dates(file_path)
    if created:
        dates.append(created)
    if modified:
        dates.append(modified)
    
    # Return the earliest date found
    if dates:
        return min(dates)
    else:
        print(f"Warning: No valid dates found for {file_path}")
        return None

def format_filename(date_obj, suffix=""):
    """Format date as YYYYMMDD_HHmmss with optional suffix."""
    base_name = date_obj.strftime("%Y%m%d_%H%M%S")
    if suffix:
        # Clean the suffix to make it filename-safe (keep spaces, remove special chars)
        clean_suffix = "".join(c for c in suffix if c.isalnum() or c in (' ', '-')).strip()
        if clean_suffix:
            base_name += f" {clean_suffix}"
    return base_name

def is_media_file(file_path):
    """Check if file is an image or video based on extension."""
    return file_path.suffix.lower() in MEDIA_EXTENSIONS

def get_unique_filename(directory, base_name, extension):
    """Generate a unique filename if the target already exists."""
    counter = 1
    original_name = base_name + extension
    new_path = directory / original_name
    
    while new_path.exists():
        new_name = f"{base_name}_{counter:02d}{extension}"
        new_path = directory / new_name
        counter += 1
    
    return new_path

def browse_for_folder():
    """Show folder selection dialog."""
    root = tk.Tk()
    root.withdraw()

    folder_path = filedialog.askdirectory(title="Select folder containing images and videos to rename")
    root.destroy()

    return folder_path

def rename_media_in_folder(folder_path, suffix=""):
    """Rename all images and videos in the top level of the specified folder."""
    folder = Path(folder_path)

    # Find all media files in the top level only
    media_files = [f for f in folder.iterdir() if f.is_file() and is_media_file(f)]

    if not media_files:
        print("No image or video files found in the selected folder.")
        return

    print(f"Found {len(media_files)} media files to process.")
    print("-" * 60)

    renamed_count = 0
    skipped_count = 0
    error_count = 0

    for media_file in media_files:
        try:
            print(f"Processing: {media_file.name}")

            # Get the earliest date
            earliest_date = get_earliest_date(media_file)

            if earliest_date is None:
                print(f"  Skipped: No valid date found")
                skipped_count += 1
                continue

            # Determine the suffix to use
            if suffix.lower() == "filename":
                # Use the original filename (without extension) as suffix
                file_suffix = media_file.stem
            else:
                file_suffix = suffix

            # Format new filename
            new_base_name = format_filename(earliest_date, file_suffix)
            new_filename = get_unique_filename(folder, new_base_name, media_file.suffix.lower())

            # Rename the file
            media_file.rename(new_filename)
            print(f"  Renamed to: {new_filename.name}")
            print(f"  Date used: {earliest_date.strftime('%Y-%m-%d %H:%M:%S')}")
            renamed_count += 1

        except Exception as e:
            print(f"  Error: {e}")
            error_count += 1

        print()

    print("-" * 60)
    print(f"Summary:")
    print(f"  Successfully renamed: {renamed_count}")
    print(f"  Skipped (no date): {skipped_count}")
    print(f"  Errors: {error_count}")
    print(f"  Total processed: {len(media_files)}")

def main():
    print("Bulk Rename Images and Videos by Date")
    print("=" * 60)
    print("This script renames images and videos to YYYYMMDD_HHmmss format")
    print("with optional suffix: YYYYMMDD_HHmmss suffix")
    print("using the earliest available date from:")
    print("- EXIF metadata (for images, if available)")
    print("- File creation date")
    print("- File modification date")
    print("=" * 60)

    if not PIL_AVAILABLE:
        print("\nWARNING: PIL (Pillow) not installed.")
        print("EXIF metadata will not be available for images.")
        print("Only file system dates will be used.")
        print("Install Pillow with: pip install Pillow")
        print("-" * 60)

    # Browse for folder
    folder_path = browse_for_folder()

    if not folder_path:
        print("No folder selected. Exiting.")
        return

    print(f"\nSelected folder: {folder_path}")

    # Prompt for optional suffix
    print("\nOptional: Enter a suffix to add after the date (leave blank for none)")
    print("Example: 'St Peter Paul' will create filenames like '20150104_134620 St Peter Paul.jpg'")
    print("Special: Enter 'filename' to use the original filename as suffix")
    print("         'IMG_0995.JPG' becomes '20150104_134620 IMG_0995.jpg'")
    suffix = input("Suffix: ").strip()

    if suffix:
        if suffix.lower() == "filename":
            print("Will use original filenames as suffixes")
        else:
            print(f"Will add suffix: '{suffix}'")
    else:
        print("No suffix will be added")

    # Process the folder
    rename_media_in_folder(folder_path, suffix)

    print("\nOperation completed!")

if __name__ == "__main__":
    main()
