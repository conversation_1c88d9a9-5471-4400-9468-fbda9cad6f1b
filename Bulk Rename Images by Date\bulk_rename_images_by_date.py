import os
import tkinter as tk
from tkinter import filedialog, messagebox
from datetime import datetime
from pathlib import Path
import shutil

# Try to import PIL for EXIF data, install if not available
try:
    from PIL import Image
    from PIL.ExifTags import TAGS
    PIL_AVAILABLE = True
except ImportError:
    PIL_AVAILABLE = False
    print("PIL (Pillow) not available. Install with: pip install Pillow")
    print("Will use file system dates only.")

# Common image extensions
IMAGE_EXTENSIONS = {'.jpg', '.jpeg', '.png', '.gif', '.bmp', '.tiff', '.tif', '.webp', '.raw', '.cr2', '.nef', '.arw', '.dng'}

def get_file_dates(file_path):
    """Get creation and modification dates from file system."""
    try:
        stat = os.stat(file_path)
        created = datetime.fromtimestamp(stat.st_ctime)
        modified = datetime.fromtimestamp(stat.st_mtime)
        return created, modified
    except Exception as e:
        print(f"Error getting file dates for {file_path}: {e}")
        return None, None

def get_exif_date(file_path):
    """Extract date from EXIF data if available."""
    if not PIL_AVAILABLE:
        return None
    
    try:
        with Image.open(file_path) as image:
            exif_data = image._getexif()
            if exif_data is not None:
                for tag_id, value in exif_data.items():
                    tag = TAGS.get(tag_id, tag_id)
                    if tag in ['DateTime', 'DateTimeOriginal', 'DateTimeDigitized']:
                        try:
                            # EXIF date format: "YYYY:MM:DD HH:MM:SS"
                            return datetime.strptime(value, "%Y:%m:%d %H:%M:%S")
                        except ValueError:
                            continue
    except Exception as e:
        print(f"Error reading EXIF data from {file_path}: {e}")
    
    return None

def get_earliest_date(file_path):
    """Get the earliest date from all available sources."""
    dates = []
    
    # Get EXIF date (usually the most accurate for photos)
    exif_date = get_exif_date(file_path)
    if exif_date:
        dates.append(exif_date)
    
    # Get file system dates
    created, modified = get_file_dates(file_path)
    if created:
        dates.append(created)
    if modified:
        dates.append(modified)
    
    # Return the earliest date found
    if dates:
        return min(dates)
    else:
        print(f"Warning: No valid dates found for {file_path}")
        return None

def format_filename(date_obj):
    """Format date as YYYYMMDD_HHmmss."""
    return date_obj.strftime("%Y%m%d_%H%M%S")

def is_image_file(file_path):
    """Check if file is an image based on extension."""
    return file_path.suffix.lower() in IMAGE_EXTENSIONS

def get_unique_filename(directory, base_name, extension):
    """Generate a unique filename if the target already exists."""
    counter = 1
    original_name = base_name + extension
    new_path = directory / original_name
    
    while new_path.exists():
        new_name = f"{base_name}_{counter:02d}{extension}"
        new_path = directory / new_name
        counter += 1
    
    return new_path

def browse_for_folder():
    """Show folder selection dialog."""
    root = tk.Tk()
    root.withdraw()
    
    folder_path = filedialog.askdirectory(title="Select folder containing images to rename")
    root.destroy()
    
    return folder_path

def rename_images_in_folder(folder_path):
    """Rename all images in the top level of the specified folder."""
    folder = Path(folder_path)
    
    # Find all image files in the top level only
    image_files = [f for f in folder.iterdir() if f.is_file() and is_image_file(f)]
    
    if not image_files:
        print("No image files found in the selected folder.")
        return
    
    print(f"Found {len(image_files)} image files to process.")
    print("-" * 60)
    
    renamed_count = 0
    skipped_count = 0
    error_count = 0
    
    for image_file in image_files:
        try:
            print(f"Processing: {image_file.name}")
            
            # Get the earliest date
            earliest_date = get_earliest_date(image_file)
            
            if earliest_date is None:
                print(f"  Skipped: No valid date found")
                skipped_count += 1
                continue
            
            # Format new filename
            new_base_name = format_filename(earliest_date)
            new_filename = get_unique_filename(folder, new_base_name, image_file.suffix.lower())
            
            # Rename the file
            image_file.rename(new_filename)
            print(f"  Renamed to: {new_filename.name}")
            print(f"  Date used: {earliest_date.strftime('%Y-%m-%d %H:%M:%S')}")
            renamed_count += 1
            
        except Exception as e:
            print(f"  Error: {e}")
            error_count += 1
        
        print()
    
    print("-" * 60)
    print(f"Summary:")
    print(f"  Successfully renamed: {renamed_count}")
    print(f"  Skipped (no date): {skipped_count}")
    print(f"  Errors: {error_count}")
    print(f"  Total processed: {len(image_files)}")

def main():
    print("Bulk Rename Images by Date")
    print("=" * 60)
    print("This script renames images to YYYYMMDD_HHmmss format")
    print("using the earliest available date from:")
    print("- EXIF metadata (if available)")
    print("- File creation date")
    print("- File modification date")
    print("=" * 60)
    
    if not PIL_AVAILABLE:
        print("\nWARNING: PIL (Pillow) not installed.")
        print("EXIF metadata will not be available.")
        print("Only file system dates will be used.")
        print("Install Pillow with: pip install Pillow")
        print("-" * 60)
    
    # Browse for folder
    folder_path = browse_for_folder()
    
    if not folder_path:
        print("No folder selected. Exiting.")
        return
    
    print(f"\nSelected folder: {folder_path}")
    
    # Confirm before proceeding
    response = input("\nProceed with renaming? (y/N): ").strip().lower()
    if response != 'y':
        print("Operation cancelled.")
        return
    
    # Process the folder
    rename_images_in_folder(folder_path)
    
    print("\nOperation completed!")

if __name__ == "__main__":
    main()
