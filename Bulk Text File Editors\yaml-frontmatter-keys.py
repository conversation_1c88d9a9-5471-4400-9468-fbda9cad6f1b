#!/usr/bin/env python3
"""
Bulk Text File Editor for Markdown Files - Add YAML Front Matter Keys

This script allows the user to browse to a folder and bulk edits all .md files found in only the top level of the folder.
It adds the following YAML front matter keys if they don't already exist:
- date
- title
- description
- updated
- areas

If a key is not present in the front matter of the file, it will be added with an empty value.
If the file doesn't have front matter, a new front matter section will be added with all the required keys.

Usage:
    python add-yaml-frontmatter-keys.py

The script will open a folder selection dialog for the user to choose the target folder.
"""

import os
import re
import tkinter as tk
from tkinter import filedialog, messagebox


# List of keys to ensure exist in the front matter
REQUIRED_KEYS = ['date', 'title', 'description', 'updated', 'areas']


def process_file(file_path):
    """
    Process a markdown file to add missing YAML front matter keys.

    Args:
        file_path (str): Path to the markdown file

    Returns:
        tuple: (bool, int) - Success status and count of keys added
    """
    try:
        # Read the file content
        with open(file_path, 'r', encoding='utf-8') as file:
            content = file.read()

        # Check if the file has front matter
        front_matter_match = re.match(r'^---\s*\n(.*?)\n---\s*\n(.*)', content, re.DOTALL)

        if front_matter_match:
            # File has front matter
            front_matter_content = front_matter_match.group(1)
            rest_of_content = front_matter_match.group(2)

            # Parse the front matter content to find existing keys
            existing_keys = set()
            for line in front_matter_content.split('\n'):
                key_match = re.match(r'^(\w+):', line)
                if key_match:
                    existing_keys.add(key_match.group(1))

            # Add missing keys
            keys_added = 0
            new_front_matter = front_matter_content

            for key in REQUIRED_KEYS:
                if key not in existing_keys:
                    new_front_matter += f"\n{key}: "
                    keys_added += 1

            if keys_added > 0:
                # Reconstruct the file content with the updated front matter
                modified_content = f"---\n{new_front_matter}\n---\n{rest_of_content}"

                # Write the modified content back to the file
                with open(file_path, 'w', encoding='utf-8') as file:
                    file.write(modified_content)

                return True, keys_added

            return False, 0
        else:
            # File doesn't have front matter, add a new one
            new_front_matter = "---\n"
            for key in REQUIRED_KEYS:
                new_front_matter += f"{key}: \n"
            new_front_matter += "---\n"

            # Combine the new front matter with the original content
            modified_content = new_front_matter + content

            # Write the modified content back to the file
            with open(file_path, 'w', encoding='utf-8') as file:
                file.write(modified_content)

            return True, len(REQUIRED_KEYS)

    except Exception as e:
        print(f"Error processing {file_path}: {str(e)}")
        return False, 0


def process_folder(folder_path):
    """
    Process all markdown files in the specified folder.

    Args:
        folder_path (str): Path to the folder containing markdown files

    Returns:
        tuple: (total_files, modified_files, total_keys_added)
    """
    # Check if the folder exists
    if not os.path.isdir(folder_path):
        messagebox.showerror("Error", f"The folder '{folder_path}' does not exist.")
        return 0, 0, 0

    print(f"Processing markdown files in: {folder_path}")

    # Get all markdown files in the top level of the folder
    md_files = [f for f in os.listdir(folder_path) if f.endswith('.md') and os.path.isfile(os.path.join(folder_path, f))]

    if not md_files:
        print("No markdown files found in the specified folder.")
        return 0, 0, 0

    print(f"Found {len(md_files)} markdown files.")

    # Process each file
    total_files_modified = 0
    total_keys_added = 0

    for file_name in md_files:
        file_path = os.path.join(folder_path, file_name)
        modified, keys_added = process_file(file_path)

        if modified:
            total_files_modified += 1
            total_keys_added += keys_added
            print(f"Modified: {file_name} - Added {keys_added} missing front matter keys")

    return len(md_files), total_files_modified, total_keys_added


def main():
    """Main function to browse for a folder and process markdown files."""
    # Create a root window but hide it
    root = tk.Tk()
    root.withdraw()

    # Show folder selection dialog
    folder_path = filedialog.askdirectory(
        title="Select Folder with Markdown Files",
        mustexist=True
    )

    # If user cancels the dialog
    if not folder_path:
        print("Folder selection cancelled.")
        return

    # Process the selected folder
    total_files, modified_files, total_keys_added = process_folder(folder_path)

    # Print summary
    if total_files > 0:
        print("\nSummary:")
        print(f"Total files processed: {total_files}")
        print(f"Files modified: {modified_files}")
        print(f"Total front matter keys added: {total_keys_added}")

        # Show a message box with the summary
        messagebox.showinfo(
            "Processing Complete",
            f"Total files processed: {total_files}\n"
            f"Files modified: {modified_files}\n"
            f"Total front matter keys added: {total_keys_added}"
        )


if __name__ == "__main__":
    main()
