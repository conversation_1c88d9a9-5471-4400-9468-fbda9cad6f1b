import tkinter as tk
from tkinter import filedialog
import os
import re

def count_lines_and_dates_in_file(filepath):
    """Count the number of lines and dates in a text file, and write lines with dates to Output.txt."""
    month_pattern = re.compile(r'\b(Jan|Feb|Mar|Apr|May|Jun|Jul|Aug|Sep|Oct|Nov|Dec)\b')
    line_count = 0
    date_count = 0

    # Prepare path for output file
    output_path = os.path.join(os.path.dirname(filepath), 'Output.txt')
    
    with open(filepath, 'r', encoding='utf-8') as file, open(output_path, 'w', encoding='utf-8') as output_file:
        for line in file:
            line_count += 1
            date_matches = month_pattern.findall(line)
            if date_matches:
                date_count += len(date_matches)
                output_file.write(line)  # Write the line containing dates to Output.txt

    return line_count, date_count

def select_file_and_count_lines_and_dates():
    """Open a file dialog to select a text file and count its lines and dates, and output lines with dates."""
    root = tk.Tk()
    root.withdraw()  # Hide the main window

    file_path = filedialog.askopenfilename(
        title="Select a text file",
        filetypes=(("Text files", "*.txt"), ("All files", "*.*"))
    )

    if file_path:
        line_count, date_count = count_lines_and_dates_in_file(file_path)
        print(f"The file '{os.path.basename(file_path)}' has {line_count} lines and contains {date_count} dates.")
        print(f"Lines containing dates have been written to 'Output.txt' in the same directory.")
    else:
        print("No file was selected.")

if __name__ == "__main__":
    select_file_and_count_lines_and_dates()