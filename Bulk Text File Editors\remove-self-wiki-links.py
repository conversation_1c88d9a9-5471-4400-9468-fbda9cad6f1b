#!/usr/bin/env python3
"""
Bulk Text File Editor for Markdown Files

This script allows the user to browse to a folder and bulk edits all .md files found in only the top level of the folder.
It removes all self wiki links (text enclosed in double brackets pointing to the same file that it is in).

Usage:
    python bulk_edit_md_files.py

The script will open a folder selection dialog for the user to choose the target folder.
"""

import os
import sys
import re
import tkinter as tk
from tkinter import filedialog, messagebox


def get_self_wiki_links(file_name):
    """
    Generate patterns for self wiki links based on the file name.

    Args:
        file_name (str): The name of the file (without extension)

    Returns:
        list: List of regex patterns to match self wiki links
    """
    # Remove extension to get the base name
    base_name = os.path.splitext(file_name)[0]

    # Create patterns for different variations of self wiki links
    patterns = [
        rf"\[\[{re.escape(base_name)}\]\]",  # [[filename]]
        rf"\[\[{re.escape(base_name)}#[^\]]*\]\]",  # [[filename#section]]
        rf"\[\[{re.escape(base_name)}\|[^\]]*\]\]",  # [[filename|alias]]
    ]

    return patterns


def process_file(file_path):
    """
    Process a markdown file to remove self wiki links.

    Args:
        file_path (str): Path to the markdown file

    Returns:
        tuple: (bool, int) - Success status and count of links removed
    """
    try:
        # Get the file name
        file_name = os.path.basename(file_path)

        # Read the file content
        with open(file_path, 'r', encoding='utf-8') as file:
            content = file.read()

        # Get patterns for self wiki links
        patterns = get_self_wiki_links(file_name)

        # Count original occurrences
        original_content = content
        link_count = 0

        # Remove self wiki links
        for pattern in patterns:
            matches = re.findall(pattern, content)
            link_count += len(matches)
            content = re.sub(pattern, '', content)

        # If changes were made, write back to the file
        if content != original_content:
            with open(file_path, 'w', encoding='utf-8') as file:
                file.write(content)
            return True, link_count

        return False, 0

    except Exception as e:
        print(f"Error processing {file_path}: {str(e)}")
        return False, 0


def process_folder(folder_path):
    """
    Process all markdown files in the specified folder.

    Args:
        folder_path (str): Path to the folder containing markdown files

    Returns:
        tuple: (total_files, modified_files, total_links_removed)
    """
    # Check if the folder exists
    if not os.path.isdir(folder_path):
        messagebox.showerror("Error", f"The folder '{folder_path}' does not exist.")
        return 0, 0, 0

    print(f"Processing markdown files in: {folder_path}")

    # Get all markdown files in the top level of the folder
    md_files = [f for f in os.listdir(folder_path) if f.endswith('.md') and os.path.isfile(os.path.join(folder_path, f))]

    if not md_files:
        print("No markdown files found in the specified folder.")
        return 0, 0, 0

    print(f"Found {len(md_files)} markdown files.")

    # Process each file
    total_files_modified = 0
    total_links_removed = 0

    for file_name in md_files:
        file_path = os.path.join(folder_path, file_name)
        modified, links_removed = process_file(file_path)

        if modified:
            total_files_modified += 1
            total_links_removed += links_removed
            print(f"Modified: {file_name} - Removed {links_removed} self wiki links")

    return len(md_files), total_files_modified, total_links_removed


def main():
    """Main function to browse for a folder and process markdown files."""
    # Create a root window but hide it
    root = tk.Tk()
    root.withdraw()

    # Show folder selection dialog
    folder_path = filedialog.askdirectory(
        title="Select Folder with Markdown Files",
        mustexist=True
    )

    # If user cancels the dialog
    if not folder_path:
        print("Folder selection cancelled.")
        return

    # Process the selected folder
    total_files, modified_files, total_links_removed = process_folder(folder_path)

    # Print summary
    if total_files > 0:
        print("\nSummary:")
        print(f"Total files processed: {total_files}")
        print(f"Files modified: {modified_files}")
        print(f"Total self wiki links removed: {total_links_removed}")

        # Show a message box with the summary
        messagebox.showinfo(
            "Processing Complete",
            f"Total files processed: {total_files}\n"
            f"Files modified: {modified_files}\n"
            f"Total self wiki links removed: {total_links_removed}"
        )


if __name__ == "__main__":
    main()
