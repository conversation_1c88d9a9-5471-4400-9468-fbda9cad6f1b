import os
import tkinter as tk
from tkinter import filedialog

def parse_transaction(lines, start_index):
    """Parse a single transaction starting from the given index."""
    date_line = lines[start_index]
    month, day = date_line.split()
    description = ""
    value = ""
    i = start_index + 1
    # Collect all lines until the next date or end of list
    while i < len(lines) and not lines[i].startswith("May ") and not lines[i].startswith("June "):
        if any(word in lines[i] for word in ["$"]):  # Value line found
            value = lines[i]
        else:
            description += lines[i] + " "
        i += 1
    return (month.upper(), day, description.strip(), value.replace('$', '').strip()), i

def format_transactions_from_file(input_file_path, output_file_path, blacklist):
    # Read the raw input text from the file
    with open(input_file_path, 'r') as file:
        raw_text = file.read()

    # Split the input into lines and filter out empty lines and blacklist words
    lines = [line.strip() for line in raw_text.strip().split("\n") if line.strip() and not any(blacklist_word in line for blacklist_word in blacklist)]

    # Process the filtered text to format it
    formatted_lines = []
    i = 0
    while i < len(lines):
        try:
            parsed_transaction, next_index = parse_transaction(lines, i)
            month, day, description, value = parsed_transaction
            if value.startswith('-'):
                value = value.replace('-', '')  # Remove minus sign
            else:
                value = f"-{value}"  # Add minus sign
            formatted_line = f"{month}\t{day}\tAmex\t{description}\t\t\t{value}"
            formatted_lines.append(formatted_line)
            i = next_index
        except ValueError as e:
            print(f"Error parsing line: '{lines[i]}' - {e}")
            i += 1  # Move to the next line to attempt to continue processing

    formatted_output = "\n".join(formatted_lines)

    # Write the formatted output to the output file
    with open(output_file_path, 'w') as file:
        file.write(formatted_output)

    print(f"Formatted transactions have been written to {output_file_path}")

def main():
    # Create a graphical window for file browsing
    root = tk.Tk()
    root.withdraw()  # Hide the main window

    # Prompt the user to select an input file
    input_file_path = filedialog.askopenfilename(title="Select the INPUT.txt file")

    if not input_file_path:
        print("No file selected, exiting.")
        return

    # Determine output file path in the same directory as the input file
    directory = os.path.dirname(input_file_path)
    output_file_path = os.path.join(directory, "Output.txt")

    # Define the blacklist
    blacklist = ["Credit", "2X Miles", "3X Points", "5X Points"]

    # Call the function with the updated paths and blacklist
    format_transactions_from_file(input_file_path, output_file_path, blacklist)

if __name__ == "__main__":
    main()