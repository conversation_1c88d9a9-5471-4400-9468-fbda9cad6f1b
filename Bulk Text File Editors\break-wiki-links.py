#!/usr/bin/env python3
"""
Bulk Text File Editor for Markdown Files - Break Wiki Links

This script allows the user to browse to a folder and bulk edits all .md files found in only the top level of the folder.
It "breaks" all wiki links encountered outside of the "# Summary" section by removing one pair of brackets.
Wiki links like [[this is a wiki link]] become [this is a wiki link] outside of the Summary section.

Usage:
    python break_wiki_links.py

The script will open a folder selection dialog for the user to choose the target folder.
"""

import os
import re
import tkinter as tk
from tkinter import filedialog, messagebox


def is_in_summary_section(content, match_position):
    """
    Check if a match position is within a Summary section.
    
    Args:
        content (str): The file content
        match_position (int): The position of the match in the content
        
    Returns:
        bool: True if the match is in a Summary section, False otherwise
    """
    # Find the position of the "# Summary" heading
    summary_match = re.search(r'^# Summary', content, re.MULTILINE)
    
    if not summary_match:
        # No Summary section found, so the match is not in it
        return False
    
    summary_start = summary_match.start()
    
    # Find the position of the next heading after Summary (if any)
    next_heading_match = re.search(r'^# ', content[summary_start + 1:], re.MULTILINE)
    
    if next_heading_match:
        summary_end = summary_start + 1 + next_heading_match.start()
    else:
        # If no next heading, Summary extends to the end of the file
        summary_end = len(content)
    
    # Check if the match position is within the Summary section
    return summary_start <= match_position < summary_end


def break_wiki_links(content):
    """
    Break wiki links outside of the Summary section.
    
    Args:
        content (str): The file content
        
    Returns:
        tuple: (modified_content, count) - Modified content and count of links broken
    """
    # Count of links broken
    broken_links_count = 0
    
    # Function to process each match
    def replace_link(match):
        nonlocal broken_links_count
        
        # Get the full match and its position
        full_match = match.group(0)
        match_position = match.start()
        
        # Check if the match is in a Summary section
        if is_in_summary_section(content, match_position):
            # In Summary section, keep the link as is
            return full_match
        else:
            # Outside Summary section, break the link
            broken_links_count += 1
            # Extract the link text (without brackets)
            link_text = match.group(1)
            # Return the broken link format
            return f"[{link_text}]"
    
    # Find and process all wiki links
    # Pattern matches [[any text]] and captures the text inside
    pattern = r'\[\[(.*?)\]\]'
    modified_content = re.sub(pattern, replace_link, content)
    
    return modified_content, broken_links_count


def process_file(file_path):
    """
    Process a markdown file to break wiki links outside of Summary section.
    
    Args:
        file_path (str): Path to the markdown file
        
    Returns:
        tuple: (bool, int) - Success status and count of links broken
    """
    try:
        # Read the file content
        with open(file_path, 'r', encoding='utf-8') as file:
            content = file.read()
        
        # Break wiki links
        modified_content, broken_links_count = break_wiki_links(content)
        
        # If changes were made, write back to the file
        if content != modified_content and broken_links_count > 0:
            with open(file_path, 'w', encoding='utf-8') as file:
                file.write(modified_content)
            return True, broken_links_count
        
        return False, 0
    
    except Exception as e:
        print(f"Error processing {file_path}: {str(e)}")
        return False, 0


def process_folder(folder_path):
    """
    Process all markdown files in the specified folder.
    
    Args:
        folder_path (str): Path to the folder containing markdown files
    
    Returns:
        tuple: (total_files, modified_files, total_links_broken)
    """
    # Check if the folder exists
    if not os.path.isdir(folder_path):
        messagebox.showerror("Error", f"The folder '{folder_path}' does not exist.")
        return 0, 0, 0
    
    print(f"Processing markdown files in: {folder_path}")
    
    # Get all markdown files in the top level of the folder
    md_files = [f for f in os.listdir(folder_path) if f.endswith('.md') and os.path.isfile(os.path.join(folder_path, f))]
    
    if not md_files:
        print("No markdown files found in the specified folder.")
        return 0, 0, 0
    
    print(f"Found {len(md_files)} markdown files.")
    
    # Process each file
    total_files_modified = 0
    total_links_broken = 0
    
    for file_name in md_files:
        file_path = os.path.join(folder_path, file_name)
        modified, links_broken = process_file(file_path)
        
        if modified:
            total_files_modified += 1
            total_links_broken += links_broken
            print(f"Modified: {file_name} - Broke {links_broken} wiki links")
    
    return len(md_files), total_files_modified, total_links_broken


def main():
    """Main function to browse for a folder and process markdown files."""
    # Create a root window but hide it
    root = tk.Tk()
    root.withdraw()
    
    # Show folder selection dialog
    folder_path = filedialog.askdirectory(
        title="Select Folder with Markdown Files",
        mustexist=True
    )
    
    # If user cancels the dialog
    if not folder_path:
        print("Folder selection cancelled.")
        return
    
    # Process the selected folder
    total_files, modified_files, total_links_broken = process_folder(folder_path)
    
    # Print summary
    if total_files > 0:
        print("\nSummary:")
        print(f"Total files processed: {total_files}")
        print(f"Files modified: {modified_files}")
        print(f"Total wiki links broken: {total_links_broken}")
        
        # Show a message box with the summary
        messagebox.showinfo(
            "Processing Complete",
            f"Total files processed: {total_files}\n"
            f"Files modified: {modified_files}\n"
            f"Total wiki links broken: {total_links_broken}"
        )


if __name__ == "__main__":
    main()
