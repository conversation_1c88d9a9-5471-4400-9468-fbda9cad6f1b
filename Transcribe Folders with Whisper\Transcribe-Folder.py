import os
import whisper
import subprocess
import tempfile
import shutil
import logging
from tkinter import filedialog, Tk
from datetime import timed<PERSON>ta
from pathlib import Path
from typing import List, Optional, Tuple

# --- Configuration ---
VIDEO_EXTENSIONS = ['.mp4', '.mov', '.mkv', '.avi', '.wmv', '.flv']
AUDIO_EXTENSIONS = ['.mp3', '.wav', '.m4a', '.aac', '.ogg', '.flac'] # Added common audio extensions, .mp4 can also be audio
# Combine video and audio extensions for scanning
MEDIA_EXTENSIONS = list(set(VIDEO_EXTENSIONS + AUDIO_EXTENSIONS + ['.mp4'])) # Ensure .mp4 is included

TRANSCRIPT_FORMAT = 'srt'  # Changed to SubRip format
CHUNK_SECONDS = 30  # Changed to 30 seconds per user request
WHISPER_MODEL = "large-v3" # Changed to the most accurate model
# Set to True if you have a compatible GPU and CUDA installed
USE_FP16 = False # Set to False if using CPU or experiencing issues

# --- Setup Logging ---
logging.basicConfig(
    level=logging.INFO,
    format='%(asctime)s - %(levelname)s - %(message)s',
    handlers=[
        logging.StreamHandler(), # Output logs to console
        # Optional: Uncomment to also log to a file
        # logging.FileHandler("transcription.log", mode='a', encoding='utf-8')
    ]
)

# --- Helper Functions ---

def check_dependencies():
    """Check if ffmpeg and ffprobe are accessible."""
    if shutil.which("ffmpeg") is None:
        logging.error("ffmpeg not found. Please install ffmpeg and ensure it's in your system's PATH.")
        return False
    if shutil.which("ffprobe") is None:
        logging.error("ffprobe not found. Please install ffmpeg (which includes ffprobe) and ensure it's in your system's PATH.")
        return False
    logging.info("ffmpeg and ffprobe found.")
    return True

def choose_folder() -> Optional[Path]:
    """Opens a dialog to choose a folder."""
    root = Tk()
    root.withdraw() # Hide the main tkinter window
    folder_path = filedialog.askdirectory(title="Select Folder Containing Media Files") # Updated title
    root.destroy() # Destroy the root window after selection
    if folder_path:
        logging.info(f"Selected folder: {folder_path}")
        return Path(folder_path)
    else:
        logging.warning("No folder selected.")
        return None

def format_time(seconds: float, srt_format: bool = False) -> str:
    """Converts seconds to HH:MM:SS or HH:MM:SS,mmm format."""
    try:
        if not isinstance(seconds, (int, float)) or seconds < 0:
            # Allow zero for start times, but log if it's an issue from elsewhere
            if seconds < 0:
                logging.warning(f"Negative time value {seconds} received. Clamping to 0.")
                seconds = 0
            # If it's not a number, it's an error
            elif not isinstance(seconds, (int, float)):
                 raise ValueError("Time must be a non-negative number.")

        # Calculate integer seconds and milliseconds
        integer_seconds = int(seconds)
        milliseconds = int((seconds - integer_seconds) * 1000)

        hours, remainder = divmod(integer_seconds, 3600)
        minutes, secs = divmod(remainder, 60)

        if srt_format:
            return f'{hours:02}:{minutes:02}:{secs:02},{milliseconds:03}'
        else:
            return f'{hours:02}:{minutes:02}:{secs:02}'
    except (ValueError, TypeError) as e:
        logging.warning(f"Could not format invalid time value: {seconds}. Error: {e}. Using default timestamp.")
        return '00:00:00,000' if srt_format else '00:00:00'


def get_audio_stream_indices(media_path: Path) -> List[int]:
    """Returns a list of audio stream indices using ffprobe."""
    command = [
        'ffprobe', '-v', 'error', '-select_streams', 'a',
        '-show_entries', 'stream=index', '-of', 'csv=p=0', str(media_path)
    ]
    try:
        result = subprocess.run(
            command,
            stdout=subprocess.PIPE, stderr=subprocess.PIPE,
            text=True, check=True, encoding='utf-8'
        )
        indices = [int(line.strip()) for line in result.stdout.strip().split('\n') if line.strip().isdigit()]
        if not indices:
             logging.warning(f"No audio streams found by ffprobe for: {media_path.name}")
        return indices
    except subprocess.CalledProcessError as e:
        logging.error(f"ffprobe failed for {media_path.name}. Error: {e.stderr.strip()}")
        return []
    except Exception as e:
        logging.error(f"An unexpected error occurred during ffprobe execution for {media_path.name}: {e}")
        return []

def extract_audio_track(media_path: Path, stream_index: int, temp_dir: Path) -> Optional[Path]:
    """Extracts a specific audio stream to a WAV file."""
    safe_media_name = "".join(c if c.isalnum() else "_" for c in media_path.stem)
    audio_filename = f"{safe_media_name}_track_{stream_index}.wav"
    audio_path = temp_dir / audio_filename

    command = [
        'ffmpeg', '-y', '-i', str(media_path),
        '-map', f'0:{stream_index}', # Corrected: Map specific stream by its absolute index
        '-ac', '1',         # Mono channel
        '-ar', '16000',      # 16kHz sample rate (optimal for Whisper)
        '-vn',              # No video output (if input is video)
        '-acodec', 'pcm_s16le', # Explicitly specify WAV codec
        str(audio_path)
    ]
    try:
        logging.info(f"Extracting audio track (absolute index {stream_index}) from {media_path.name}...")
        result = subprocess.run(
            command,
            stdout=subprocess.PIPE, stderr=subprocess.PIPE,
            text=True, check=False, encoding='utf-8'
        )
        if result.returncode != 0:
            logging.error(f"ffmpeg failed to extract track {stream_index} from {media_path.name}. Error: {result.stderr.strip()}")
            return None
        if not audio_path.exists() or audio_path.stat().st_size == 0:
            logging.warning(f"Extracted audio file for stream {stream_index} is missing or empty: {audio_path}")
            return None
        logging.info(f"Successfully extracted audio to: {audio_path.name}")
        return audio_path
    except FileNotFoundError:
         logging.error(f"ffmpeg command failed. Is ffmpeg installed and in PATH? Command: {' '.join(command)}")
         return None
    except Exception as e:
        logging.error(f"An unexpected error occurred during ffmpeg extraction for track {stream_index} of {media_path.name}: {e}")
        return None

# --- Main Processing Function ---

def transcribe_media_file(media_path: Path, model):
    """Transcribes audio tracks from a single media file into SRT format."""
    logging.info(f"Processing: {media_path.name}")
    stream_indices = get_audio_stream_indices(media_path)

    if not stream_indices:
        # For pure audio files that ffprobe might not list an index for in this way,
        # or if it's a common audio format, try treating the file as a single audio track.
        # This typically happens if the audio file has no explicit stream selection needed.
        # We can assign a default stream index (e.g., 'a:0' for ffmpeg if it's simpler, or assume index 0)
        # For now, let's assume ffprobe should find an index if it's a valid audio stream.
        # If it's a simple audio file, ffprobe should still return its stream index (often 0).
        logging.warning(f"No audio tracks found by ffprobe for {media_path.name}. Skipping.")
        return

    num_tracks = len(stream_indices)
    base_name = media_path.stem

    with tempfile.TemporaryDirectory(prefix=f"whisper_{base_name}_") as temp_dir_str:
        temp_dir = Path(temp_dir_str)
        logging.info(f"Created temporary directory: {temp_dir}")

        for track_num, stream_idx in enumerate(stream_indices): # Use enumerate for track count if needed for naming
            audio_path = extract_audio_track(media_path, stream_idx, temp_dir)

            if not audio_path:
                logging.warning(f"Skipping transcription for track index {stream_idx} of {media_path.name} due to extraction failure.")
                continue

            try:
                logging.info(f"Transcribing audio track index {stream_idx} from {audio_path.name} (Model: {WHISPER_MODEL})...")
                result = model.transcribe(str(audio_path), fp16=USE_FP16)
                logging.info(f"Transcription complete for track {stream_idx}.")

            except Exception as e:
                logging.error(f"Error during Whisper transcription for track {stream_idx} of {media_path.name}: {e}")
                continue

            # --- Process and Write SRT Transcript ---
            segments = result.get('segments', [])
            
            # Determine output filename
            if num_tracks > 1:
                # Use absolute stream index for clarity in filename if multiple streams
                output_filename = f"{base_name} (Track {stream_idx}).{TRANSCRIPT_FORMAT}"
            else:
                output_filename = f"{base_name}.{TRANSCRIPT_FORMAT}"
            output_path = media_path.parent / output_filename

            logging.info(f"Writing {TRANSCRIPT_FORMAT} transcript to: {output_path}")
            try:
                with open(output_path, 'w', encoding='utf-8') as f:
                    if not segments:
                        logging.warning(f"No segments found in transcription for track {stream_idx} of {media_path.name}.")
                        # Write a placeholder SRT entry for empty transcript
                        f.write("1\n")
                        start_time_str = format_time(0, srt_format=True)
                        # Use a minimal duration like 1s or CHUNK_SECONDS for the placeholder
                        end_time_str = format_time(min(1.0, CHUNK_SECONDS), srt_format=True) 
                        f.write(f"{start_time_str} --> {end_time_str}\n")
                        f.write("[No speech detected or transcribed]\n\n")
                    else:
                        srt_idx = 1
                        # Determine total duration based on last segment's end time for chunking
                        max_segment_end_time = 0
                        if segments:
                           valid_end_times = [s.get('end') for s in segments if s.get('end') is not None]
                           if valid_end_times:
                               max_segment_end_time = max(valid_end_times)
                           else: # Fallback if no end times, use start times
                               valid_start_times = [s.get('start') for s in segments if s.get('start') is not None]
                               if valid_start_times: max_segment_end_time = max(valid_start_times)


                        num_total_chunks = (int(max_segment_end_time) // CHUNK_SECONDS) + 1

                        for i in range(num_total_chunks):
                            chunk_start_seconds = float(i * CHUNK_SECONDS)
                            chunk_end_seconds = float((i + 1) * CHUNK_SECONDS)
                            
                            current_chunk_texts = []
                            actual_min_start_this_chunk = chunk_end_seconds # for adjusting SRT timing if needed
                            actual_max_end_this_chunk = chunk_start_seconds # for adjusting SRT timing if needed

                            for segment in segments:
                                seg_start = segment.get('start')
                                seg_end = segment.get('end')
                                text = segment.get('text', '').strip()

                                if seg_start is not None and text:
                                    # Collect segments whose start time falls into this CHUNK_SECONDS window
                                    if chunk_start_seconds <= seg_start < chunk_end_seconds:
                                        current_chunk_texts.append(text)
                                        # Optional: adjust chunk times to actual speech times if desired
                                        # actual_min_start_this_chunk = min(actual_min_start_this_chunk, seg_start)
                                        # if seg_end is not None:
                                        #    actual_max_end_this_chunk = max(actual_max_end_this_chunk, seg_end)


                            full_text_for_chunk = " ".join(current_chunk_texts).strip()

                            # Use fixed 30-second chunk boundaries for SRT entries as per requirement
                            srt_start_time = format_time(chunk_start_seconds, srt_format=True)
                            srt_end_time = format_time(chunk_end_seconds, srt_format=True)
                            
                            # Only write entry if there's text, or always write fixed chunks
                            # Current approach: always write fixed chunks, text can be empty
                            f.write(f"{srt_idx}\n")
                            f.write(f"{srt_start_time} --> {srt_end_time}\n")
                            f.write(f"{full_text_for_chunk if full_text_for_chunk else ''}\n\n") # Write empty line if no text
                            srt_idx += 1
            except IOError as e:
                logging.error(f"Error writing transcript file {output_path}: {e}")
            except Exception as e:
                 logging.error(f"An unexpected error occurred while writing transcript for track {stream_idx}: {e}")

        logging.info(f"Finished processing track {stream_idx} for {media_path.name}.")
    logging.info(f"Finished processing all tracks for {media_path.name}. Temporary directory {temp_dir} removed.")


# --- Main Execution ---

def main():
    """Main function to select folder and process media files."""
    if not check_dependencies():
        return

    folder_path = choose_folder()
    if not folder_path:
        return

    try:
        logging.info(f"Loading Whisper model: {WHISPER_MODEL}...")
        logging.info("This may take some time, especially for larger models like 'large-v3'.")
        logging.info("Ensure you have sufficient RAM/VRAM.")
        model = whisper.load_model(WHISPER_MODEL)
        logging.info("Whisper model loaded successfully.")
    except Exception as e:
        logging.error(f"Failed to load Whisper model '{WHISPER_MODEL}'. Error: {e}")
        logging.error("Please check model name, internet connection (for first download), and system resources (RAM/VRAM).")
        return

    media_files_found = []
    for item_name in os.listdir(folder_path):
        item_path = folder_path / item_name
        if item_path.is_file() and item_path.suffix.lower() in MEDIA_EXTENSIONS:
            media_files_found.append(item_path)
        elif item_path.is_file():
            logging.debug(f"Skipping non-media file: {item_path.name} (extension {item_path.suffix.lower()})")
        else:
            logging.debug(f"Skipping non-file item (e.g., directory): {item_path.name}")
    
    if not media_files_found:
        logging.warning(f"No media files found with extensions {MEDIA_EXTENSIONS} in {folder_path}")
        return
    
    logging.info(f"Found {len(media_files_found)} total media files in {folder_path}.")

    files_to_transcribe = []
    for media_file in media_files_found:
        # Output filename based on TRANSCRIPT_FORMAT (srt)
        # If multiple audio tracks, transcribe_media_file will add (Track X)
        # For checking existence, we assume the primary transcript name (without Track X)
        # or check for any (Track X) variants if needed, but simpler to check base name.
        # The current transcribe_media_file handles naming for multiple tracks internally.
        # Here, we check for the single-track output name.
        # If a multi-track file was already processed, it would have (Track X).srt files.
        # This check might miss re-transcribing if only some tracks of a multi-track file were done.
        # A more robust check would list all .srt files and match them back.
        # For now, check for the base .srt file.
        
        # Simplified check: if a file like "video.srt" exists, assume it's transcribed.
        # If "video (Track 0).srt" exists, this check won't see "video.srt" and might re-process.
        # This is a limitation of the simple check.
        # A better check would be: if any file starting with media_file.stem and ending with .srt exists.
        
        # Let's check for the most common case: media_file.stem + .srt
        expected_srt_filename = f"{media_file.stem}.{TRANSCRIPT_FORMAT}"
        expected_srt_path = media_file.parent / expected_srt_filename
        
        # More robust check: see if ANY srt file for this stem exists (covers multi-track)
        has_any_srt = False
        for existing_file in media_file.parent.glob(f"{media_file.stem}*.{TRANSCRIPT_FORMAT}"):
            has_any_srt = True
            break # Found at least one srt for this media file

        if not has_any_srt:
            files_to_transcribe.append(media_file)
        else:
            logging.info(f"At least one .srt transcript already exists for {media_file.name}. Skipping.")

    if not files_to_transcribe:
        logging.info(f"All {len(media_files_found)} media files appear to have corresponding .srt transcripts.")
        return

    logging.info(f"Number of media files to transcribe (missing .srt): {len(files_to_transcribe)} out of {len(media_files_found)} total.")

    processed_files_count = 0
    failed_files_count = 0

    for media_path in files_to_transcribe:
        try:
            transcribe_media_file(media_path, model)
            processed_files_count += 1
        except Exception as e:
            logging.error(f"!!! Critical error processing {media_path.name}. Skipping this file. Error: {e}", exc_info=True)
            failed_files_count += 1
        finally:
            # Optional: Add garbage collection or VRAM clearing if memory issues persist
            # import gc; import torch;
            # if torch.cuda.is_available(): torch.cuda.empty_cache()
            # gc.collect()
            pass

    logging.info("="*20 + " Processing Summary " + "="*20)
    logging.info(f"Total media files found: {len(media_files_found)}")
    logging.info(f"Media files queued for transcription: {len(files_to_transcribe)}")
    logging.info(f"Successfully processed/attempted: {processed_files_count}")
    logging.info(f"Failed due to critical errors: {failed_files_count}")
    logging.info("Script finished.")


if __name__ == '__main__':
    main()
