import os
import yaml

def update_yaml_value(file_path, search_key, find_value, replace_value):
    try:
        with open(file_path, 'r+', encoding='utf-8') as file:
            content = file.read()
            
            # Check if file has YAML front matter
            if content.startswith('---'):
                front_matter_end = content.find('---', 3)
                if front_matter_end == -1:
                    return False, False
                front_matter = content[3:front_matter_end]
                front_matter_dict = yaml.safe_load(front_matter)
                
                if front_matter_dict.get(search_key) == find_value:
                    front_matter_dict[search_key] = replace_value
                    
                    new_front_matter = yaml.dump(front_matter_dict, default_flow_style=False)
                    new_front_matter = f"---\n{new_front_matter}---\n"
                    
                    file.seek(0)
                    file.write(new_front_matter + content[front_matter_end+3:])
                    file.truncate()
                    
                    return True, True
                elif search_key in front_matter_dict:
                    return True, False
    except yaml.YAMLError as e:
        print(f"Error parsing YAML in file {file_path}: {e}")
    return False, False

def scan_and_replace_values(directory, search_key, find_value, replace_value):
    scanned_files = 0
    files_with_key_value = 0
    replacement_success = 0

    for filename in os.listdir(directory):
        if filename.endswith('.md'):
            file_path = os.path.join(directory, filename)
            scanned_files += 1
            has_key, replaced = update_yaml_value(file_path, search_key, find_value, replace_value)
            if has_key:
                files_with_key_value += 1
            if replaced:
                replacement_success += 1

    print(f"Scanned {scanned_files} files.")
    print(f"Files with the key '{search_key}': {files_with_key_value}")
    print(f"Files with '{search_key}: {find_value}': {files_with_key_value - (files_with_key_value - replacement_success)}")
    print(f"Successful replacements: {replacement_success}")

if __name__ == "__main__":
    directory = "Test"  # Replace with your directory if different
    search_key = input("Enter the key to find: ").strip()
    find_value = input("Enter the value to find: ").strip()
    replace_value = input("Enter the value to replace with: ").strip()
    
    scan_and_replace_values(directory, search_key, find_value, replace_value)
