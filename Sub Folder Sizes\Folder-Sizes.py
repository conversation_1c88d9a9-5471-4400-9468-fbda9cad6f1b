import os
import tkinter as tk
from tkinter import filedialog

def get_folder_size(path):
    total_size = 0
    for dirpath, dirnames, filenames in os.walk(path):
        for f in filenames:
            try:
                fp = os.path.join(dirpath, f)
                total_size += os.path.getsize(fp)
            except (FileNotFoundError, PermissionError):
                continue
    return total_size

def list_folders_by_size(base_path):
    print(f"\nAnalyzing subfolders in: {base_path}\n")
    folder_sizes = []
    for entry in os.scandir(base_path):
        if entry.is_dir(follow_symlinks=False):
            size = get_folder_size(entry.path)
            folder_sizes.append((entry.name, size))

    # Sort folders by size from largest to smallest
    folder_sizes.sort(key=lambda x: x[1], reverse=True)

    if not folder_sizes:
        print("No subfolders found.")
        return

    print("Subfolders by size (largest to smallest):")
    print("-" * 50)
    for name, size in folder_sizes:
        size_mb = size / (1024 * 1024)
        size_gb = size_mb / 1024

        if size_gb >= 1:
            print(f"{name}: {size_gb:.2f} GB")
        else:
            print(f"{name}: {size_mb:.2f} MB")
    print("-" * 50)

def browse_for_folder():
    # Hide the main tkinter window
    root = tk.Tk()
    root.withdraw()

    # Show the folder selection dialog
    folder_path = filedialog.askdirectory(title="Select a folder to analyze")

    # Close the tkinter instance
    root.destroy()

    return folder_path

if __name__ == "__main__":
    print("Folder Size Analyzer")
    print("=" * 50)

    # Let the user browse for a folder
    base_directory = browse_for_folder()

    if base_directory:
        list_folders_by_size(base_directory)
    else:
        print("No folder selected. Exiting program.")
